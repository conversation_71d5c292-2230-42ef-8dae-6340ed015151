

from pydantic import BaseModel, Field
from agents import FunctionTool, RunContextWrapper
from typing import Any, Optional
import os

class FileEditArgs(BaseModel):
    file_path: str = Field(
        ...,
        description="The absolute path to the file to modify. Must start with '/'."
    )
    old_string: str = Field(
        ...,
        description='The exact literal text to replace, preferably unescaped. For single replacements (default), include at least 3 lines of context BEFORE and AFTER the target text, matching whitespace and indentation precisely. For multiple replacements, specify expected_replacements parameter. If this string is not the exact literal text (i.e. you escaped it) or does not match exactly, the tool will fail.'
    )
    new_string: str = Field(
        ...,
        description='The exact literal text to replace `old_string` with, preferably unescaped. Provide the EXACT text. Ensure the resulting code is correct and idiomatic.'
    )
    expected_replacements: Optional[int] = Field(
        1,
        description='Number of replacements expected. Defaults to 1 if not specified. Use when you want to replace multiple occurrences.'
    )

async def run_file_edit(
    ctx: RunContextWrapper[Any],
    args: str
) -> str:
    """
    Replaces text within a file.
    """
    parsed = FileEditArgs.model_validate_json(args)
    file_path = parsed.file_path
    old_string = parsed.old_string
    new_string = parsed.new_string
    expected_replacements = parsed.expected_replacements

    if not os.path.isabs(file_path):
        return f"Error: File path must be absolute: {file_path}"

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        actual_replacements = content.count(old_string)

        if actual_replacements == 0:
            return f"Error: Could not find the string to replace."

        if expected_replacements is not None and actual_replacements != expected_replacements:
            return f"Error: Expected {expected_replacements} occurrences but found {actual_replacements}."

        new_content = content.replace(old_string, new_string, expected_replacements if expected_replacements is not None else -1)

        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)

        return f"Successfully modified file: {file_path} ({actual_replacements} replacements)."

    except FileNotFoundError:
        return f"Error: File not found at {file_path}"
    except Exception as e:
        return f"Error editing file: {e}"

file_edit_tool = FunctionTool(
    name="replace",
    description='Replaces text within a file. By default, replaces a single occurrence, but can replace multiple occurrences when `expected_replacements` is specified. This tool requires providing significant context around the change to ensure precise targeting. Always use the read_file tool to examine the file\'s current content before attempting a text replacement.',
    params_json_schema=FileEditArgs.model_json_schema(),
    on_invoke_tool=run_file_edit,
)
