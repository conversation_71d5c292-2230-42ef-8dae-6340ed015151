from pydantic import BaseModel, Field
from agents import FunctionTool, RunContextWrapper
from typing import Any

class ReapplyArgs(BaseModel):
    target_file: str = Field(..., description="File to reapply last edit.")

async def run_reapply(
    ctx: RunContextWrapper[Any],
    args: str
) -> str:
    """
    Reapply the last edit to the specified file using a smarter model. The actual re-application logic is handled by the agent framework based on this signal.
    """
    parsed = ReapplyArgs.model_validate_json(args)
    target_file = parsed.target_file
    return f"Requesting to re-apply the last edit on '{target_file}'."

reapply_tool = FunctionTool(
    name="reapply",
    description="Reapply the last edit to the specified file using a smarter model.",
    params_json_schema=ReapplyArgs.model_json_schema(),
    on_invoke_tool=run_reapply,
)
