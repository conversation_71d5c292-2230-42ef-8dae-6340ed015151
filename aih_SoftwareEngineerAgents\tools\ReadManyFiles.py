from pydantic import BaseModel, Field
from agents import FunctionTool, RunContextWrapper
from typing import Any, Optional, List
import os
import glob

class ReadManyFilesArgs(BaseModel):
    paths: List[str] = Field(
        ...,
        description="Required. An array of glob patterns or paths relative to the tool's target directory. Examples: ['src/**/*.ts'], ['README.md', 'docs/']"
    )
    exclude: Optional[List[str]] = Field(
        [],
        description='Optional. Glob patterns for files/directories to exclude. Added to default excludes if useDefaultExcludes is true. Example: ["**/*.log", "temp/"]'
    )
    include: Optional[List[str]] = Field(
        [],
        description='Optional. Additional glob patterns to include. These are merged with `paths`. Example: ["*.test.ts"] to specifically add test files if they were broadly excluded.'
    )
    recursive: Optional[bool] = Field(
        True,
        description='Optional. Whether to search recursively (primarily controlled by `**` in glob patterns). Defaults to true.'
    )
    respect_git_ignore: Optional[bool] = Field(
        True,
        description='Optional. Whether to respect .gitignore patterns when discovering files. Only available in git repositories. Defaults to true.'
    )
    useDefaultExcludes: Optional[bool] = Field(
        True,
        description='Optional. Whether to apply a list of default exclusion patterns (e.g., node_modules, .git, binary files). Defaults to true.'
    )

async def run_read_many_files(
    ctx: RunContextWrapper[Any],
    args: str
) -> str:
    """
    Reads content from multiple files specified by paths or glob patterns.
    """
    parsed = ReadManyFilesArgs.model_validate_json(args)
    
    all_files = set()
    for pattern in parsed.paths + parsed.include:
        # This simplified version doesn't handle gitignore or default excludes
        files = glob.glob(pattern, recursive=parsed.recursive)
        all_files.update(files)

    if parsed.exclude:
        excluded_files = set()
        for pattern in parsed.exclude:
            excluded_files.update(glob.glob(pattern, recursive=parsed.recursive))
        all_files -= excluded_files

    content = ""
    for file_path in all_files:
        if os.path.isfile(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content += f"--- {file_path} ---\n"
                    content += f.read()
                    content += "\n\n"
            except Exception:
                # Ignore files that can't be read
                pass
    
    return content if content else "No files were read."

read_many_files_tool = FunctionTool(
    name="read_many_files",
    description='Reads content from multiple files specified by paths or glob patterns within a configured target directory. For text files, it concatenates their content into a single string.',
    params_json_schema=ReadManyFilesArgs.model_json_schema(),
    on_invoke_tool=run_read_many_files,
)