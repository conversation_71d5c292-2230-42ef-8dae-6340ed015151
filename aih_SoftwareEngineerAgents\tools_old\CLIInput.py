from pydantic import BaseModel, Field
from agents import FunctionTool, RunContextWrapper
from typing import Optional, List, Dict
import questionary
import json
import re
from typing import Any

class GetInputTool(BaseModel):
    prompt: str = Field(..., description="Prompt to display to the user on the CLI.")

async def run_get_input(ctx: RunContextWrapper[Any], args: str) -> str:
    parsed = GetInputTool.model_validate_json(args)
    return input(parsed.prompt)

cli_input_tool = FunctionTool(
    name="cli_input",
    description="Gets input from the user via console.",
    params_json_schema=GetInputTool.model_json_schema(),
    on_invoke_tool=run_get_input,
)