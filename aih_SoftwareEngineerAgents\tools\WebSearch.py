
from pydantic import BaseModel, Field
from agents import FunctionTool, RunContextWrapper
from typing import Any, Optional
from serpapi import GoogleSearch

class WebSearchArgs(BaseModel):
    query: str = Field(
        ...,
        description="The search query to find information on the web."
    )

async def run_web_search(
    ctx: RunContextWrapper[Any],
    args: str
) -> str:
    """
    Performs a web search using Google Search (via the Gemini API) and returns the results.
    """
    parsed = WebSearchArgs.model_validate_json(args)
    query = parsed.query

    try:
        params = {
            "q": query,
            "api_key": os.environ["SERPAPI_API_KEY"]
        }
        search = GoogleSearch(params)
        results = search.get_dict()
        
        if "organic_results" in results:
            organic_results = results["organic_results"]
            output = f"Search results for '{query}':\n"
            for result in organic_results[:5]: # Limiting to top 5 results
                output += f"- Title: {result.get('title', 'N/A')}\n"
                output += f"  Link: {result.get('link', 'N/A')}\n"
                output += f"  Snippet: {result.get('snippet', 'N/A')}\n\n"
            return output
        else:
            return f"No results found for '{query}'."

    except Exception as e:
        return f"An error occurred during web search: {e}"

web_search_tool = FunctionTool(
    name="google_web_search",
    description='Performs a web search using Google Search (via the Gemini API) and returns the results. This tool is useful for finding information on the internet based on a query.',
    params_json_schema=WebSearchArgs.model_json_schema(),
    on_invoke_tool=run_web_search,
)
