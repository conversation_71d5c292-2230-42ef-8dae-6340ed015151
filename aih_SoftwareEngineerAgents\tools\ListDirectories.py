from pydantic import BaseModel, Field
from agents import FunctionTool, RunContextWrapper
from typing import Any, Optional, List
import os

class ListDirectoriesArgs(BaseModel):
    path: str = Field(
        ...,
        description="The absolute path to the directory to list (must be absolute, not relative)"
    )
    ignore: Optional[List[str]] = Field(
        None,
        description="List of glob patterns to ignore"
    )
    respect_git_ignore: Optional[bool] = Field(
        True,
        description="Optional: Whether to respect .gitignore patterns when listing files. Only available in git repositories. Defaults to true."
    )

async def run_list_directories(
    ctx: RunContextWrapper[Any],
    args: str
) -> str:
    """
    Lists the names of files and subdirectories directly within a specified directory path.
    """
    parsed = ListDirectoriesArgs.model_validate_json(args)
    path = parsed.path
    ignore = parsed.ignore
    
    if not os.path.isabs(path):
        return f"Error: Path must be absolute: {path}"
    
    if not os.path.isdir(path):
        return f"Error: Path is not a directory: {path}"

    try:
        entries = os.listdir(path)
        if ignore:
            # This is a simple implementation and doesn't support full glob patterns.
            # For a more robust solution, the `glob` library should be used with patterns.
            entries = [e for e in entries if e not in ignore]

        return "\n".join(entries)
    except Exception as e:
        return f"Error listing directory: {e}"

list_directories_tool = FunctionTool(
    name="list_directory",
    description='Lists the names of files and subdirectories directly within a specified directory path. Can optionally ignore entries matching provided glob patterns.',
    params_json_schema=ListDirectoriesArgs.model_json_schema(),
    on_invoke_tool=run_list_directories,
)