"""
Custom exceptions for absolute path operations.

This module contains specialized exception classes for handling various
path-related errors that can occur during file and directory operations.
"""

import os
from typing import Optional, Union, List


class AbsolutePathError(Exception):
    """Base exception class for all absolute path related errors."""

    def __init__(self, message: str, path: Optional[str] = None, error_code: Optional[str] = None):
        """
        Initialize the AbsolutePathError.

        Args:
            message: Human-readable error message
            path: The path that caused the error (if applicable)
            error_code: Optional error code for programmatic handling
        """
        super().__init__(message)
        self.path = path
        self.error_code = error_code
        self.message = message

    def __str__(self) -> str:
        if self.path:
            return f"{self.message} (Path: {self.path})"
        return self.message


class InvalidAbsolutePathError(AbsolutePathError):
    """Raised when a path is not a valid absolute path."""

    def __init__(self, path: str, reason: Optional[str] = None):
        """
        Initialize InvalidAbsolutePathError.

        Args:
            path: The invalid path
            reason: Optional reason why the path is invalid
        """
        message = f"Invalid absolute path: '{path}'"
        if reason:
            message += f" - {reason}"
        super().__init__(message, path, "INVALID_ABSOLUTE_PATH")


class PathNotFoundError(AbsolutePathError):
    """Raised when a specified absolute path does not exist."""

    def __init__(self, path: str, operation: Optional[str] = None):
        """
        Initialize PathNotFoundError.

        Args:
            path: The path that was not found
            operation: Optional operation that was being attempted
        """
        message = f"Path not found: '{path}'"
        if operation:
            message += f" during {operation}"
        super().__init__(message, path, "PATH_NOT_FOUND")


class PathAccessDeniedError(AbsolutePathError):
    """Raised when access to a path is denied due to permissions."""

    def __init__(self, path: str, operation: str, permission_type: str = "read/write"):
        """
        Initialize PathAccessDeniedError.

        Args:
            path: The path with access issues
            operation: The operation that was denied
            permission_type: Type of permission that was denied
        """
        message = f"Access denied to '{path}' for {operation} operation ({permission_type} permission required)"
        super().__init__(message, path, "ACCESS_DENIED")


class PathTypeError(AbsolutePathError):
    """Raised when a path exists but is not the expected type (file vs directory)."""

    def __init__(self, path: str, expected_type: str, actual_type: str):
        """
        Initialize PathTypeError.

        Args:
            path: The path with type mismatch
            expected_type: Expected path type ('file' or 'directory')
            actual_type: Actual path type found
        """
        message = f"Path '{path}' is a {actual_type}, but expected a {expected_type}"
        super().__init__(message, path, "PATH_TYPE_MISMATCH")


class PathTooLongError(AbsolutePathError):
    """Raised when a path exceeds the maximum allowed length for the filesystem."""

    def __init__(self, path: str, max_length: int, actual_length: int):
        """
        Initialize PathTooLongError.

        Args:
            path: The path that is too long
            max_length: Maximum allowed path length
            actual_length: Actual path length
        """
        message = f"Path length ({actual_length}) exceeds maximum allowed ({max_length}): '{path}'"
        super().__init__(message, path, "PATH_TOO_LONG")


class InvalidPathCharacterError(AbsolutePathError):
    """Raised when a path contains invalid characters for the current filesystem."""

    def __init__(self, path: str, invalid_chars: List[str]):
        """
        Initialize InvalidPathCharacterError.

        Args:
            path: The path containing invalid characters
            invalid_chars: List of invalid characters found
        """
        chars_str = "', '".join(invalid_chars)
        message = f"Path contains invalid characters ['{chars_str}']: '{path}'"
        super().__init__(message, path, "INVALID_PATH_CHARACTERS")


class PathResolutionError(AbsolutePathError):
    """Raised when a path cannot be resolved to an absolute path."""

    def __init__(self, path: str, reason: str):
        """
        Initialize PathResolutionError.

        Args:
            path: The path that could not be resolved
            reason: Reason why resolution failed
        """
        message = f"Failed to resolve path '{path}': {reason}"
        super().__init__(message, path, "PATH_RESOLUTION_FAILED")


class CircularSymlinkError(AbsolutePathError):
    """Raised when a circular symlink is detected in the path."""

    def __init__(self, path: str, symlink_chain: Optional[List[str]] = None):
        """
        Initialize CircularSymlinkError.

        Args:
            path: The path where circular symlink was detected
            symlink_chain: Optional chain of symlinks that form the circle
        """
        message = f"Circular symlink detected in path: '{path}'"
        if symlink_chain:
            chain_str = " -> ".join(symlink_chain)
            message += f" (Chain: {chain_str})"
        super().__init__(message, path, "CIRCULAR_SYMLINK")


class PathOperationError(AbsolutePathError):
    """Raised when a file system operation fails on a path."""

    def __init__(self, path: str, operation: str, underlying_error: Optional[Exception] = None):
        """
        Initialize PathOperationError.

        Args:
            path: The path where operation failed
            operation: The operation that failed
            underlying_error: The underlying exception that caused the failure
        """
        message = f"Operation '{operation}' failed on path '{path}'"
        if underlying_error:
            message += f": {str(underlying_error)}"
        super().__init__(message, path, "OPERATION_FAILED")
        self.underlying_error = underlying_error


class PathValidationError(AbsolutePathError):
    """Raised when path validation fails according to custom rules."""

    def __init__(self, path: str, validation_rule: str, details: Optional[str] = None):
        """
        Initialize PathValidationError.

        Args:
            path: The path that failed validation
            validation_rule: The validation rule that was violated
            details: Optional additional details about the validation failure
        """
        message = f"Path validation failed for rule '{validation_rule}': '{path}'"
        if details:
            message += f" - {details}"
        super().__init__(message, path, "VALIDATION_FAILED")


# Utility functions for common path validation scenarios

def validate_absolute_path(path: Union[str, os.PathLike]) -> str:
    """
    Validate that a path is absolute and return it as a string.

    Args:
        path: Path to validate

    Returns:
        The path as a string if valid

    Raises:
        InvalidAbsolutePathError: If the path is not absolute
        PathValidationError: If the path is invalid for other reasons
    """
    if not isinstance(path, (str, os.PathLike)):
        raise PathValidationError(str(path), "type_check", "Path must be string or PathLike object")

    path_str = str(path)

    if not path_str:
        raise InvalidAbsolutePathError(path_str, "Empty path provided")

    if not os.path.isabs(path_str):
        raise InvalidAbsolutePathError(path_str, "Path is not absolute")

    return path_str


def ensure_path_exists(path: str, path_type: Optional[str] = None) -> None:
    """
    Ensure that a path exists and optionally check its type.

    Args:
        path: Absolute path to check
        path_type: Optional type check ('file', 'directory', or None)

    Raises:
        PathNotFoundError: If the path doesn't exist
        PathTypeError: If the path exists but is not the expected type
    """
    if not os.path.exists(path):
        raise PathNotFoundError(path)

    if path_type:
        if path_type == 'file' and not os.path.isfile(path):
            actual_type = 'directory' if os.path.isdir(path) else 'other'
            raise PathTypeError(path, 'file', actual_type)
        elif path_type == 'directory' and not os.path.isdir(path):
            actual_type = 'file' if os.path.isfile(path) else 'other'
            raise PathTypeError(path, 'directory', actual_type)


def check_path_permissions(path: str, operation: str) -> None:
    """
    Check if the current user has the required permissions for an operation.

    Args:
        path: Absolute path to check
        operation: Operation type ('read', 'write', 'execute')

    Raises:
        PathAccessDeniedError: If permission is denied
        PathNotFoundError: If the path doesn't exist
    """
    if not os.path.exists(path):
        raise PathNotFoundError(path, f"permission check for {operation}")

    if operation == 'read' and not os.access(path, os.R_OK):
        raise PathAccessDeniedError(path, operation, "read")
    elif operation == 'write' and not os.access(path, os.W_OK):
        raise PathAccessDeniedError(path, operation, "write")
    elif operation == 'execute' and not os.access(path, os.X_OK):
        raise PathAccessDeniedError(path, operation, "execute")