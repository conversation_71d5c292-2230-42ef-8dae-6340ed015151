from pydantic import BaseModel, Field
from agents import <PERSON><PERSON><PERSON><PERSON>, RunContextWrap<PERSON>, Agent, <PERSON>
from typing import Any, Optional, List
import os
import re
import difflib
from concurrent.futures import ThreadPoolExecutor
import asyncio


class FileAnalysisArgs(BaseModel):
    file_path: str = Field(..., description="Path to the file to analyze")
    search_pattern: Optional[str] = Field(None, description="Optional pattern to search in file")

async def analyze_file(ctx: RunContextWrapper[Any], args: str) -> str:
    """Analyze a file's structure and content, optionally searching for patterns"""
    parsed = FileAnalysisArgs.model_validate_json(args)
    
    try:
        with open(parsed.file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        analysis = {
            'path': parsed.file_path,
            'size': os.path.getsize(parsed.file_path),
            'lines': len(content.splitlines()),
            'file_type': os.path.splitext(parsed.file_path)[1]
        }
        
        if parsed.search_pattern:
            matches = re.finditer(parsed.search_pattern, content, re.MULTILINE)
            analysis['matches'] = [{
                'line': m.group(0),
                'start': m.start(),
                'end': m.end()
            } for m in matches]
        
        return str(analysis)
    
    except Exception as e:
        return f"Analysis error: {e}"

file_analysis_tool = FunctionTool(
    name="analyze_file",
    description="Analyze file structure and search for patterns. It will read entire file content, calculate size and number of lines and etc metadata.",
    params_json_schema=FileAnalysisArgs.model_json_schema(),
    on_invoke_tool=analyze_file
)
