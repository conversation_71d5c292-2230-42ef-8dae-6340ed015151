from typing import List, Optional
from pydantic import BaseModel, Field
from agents import FunctionTool
import os
from agents import RunContextWrapper
from typing import Any
from agents import Agent

class CodebaseSearchArgs(BaseModel):
    # explanation: Optional[str] = Field(
    #     None, 
    #     description="One sentence why this tool is used."
    # )
    query: str = Field(
        ..., 
        description="The search query to find relevant code."
    )
    target_directories: Optional[List[str]] = Field(
        None, 
        description="Directories to search."
    )

async def run_codebase_search(
    ctx: RunContextWrapper[Any], 
    args: str
) -> str:
    """
    Find snippets of code most relevant to the search query using semantic search.
    """
    parsed = CodebaseSearchArgs.model_validate_json(args)
    query = parsed.query
    target_directories = parsed.target_directories
    # explanation = parsed.explanation
    results = []
    if target_directories is None:
        search_dirs = ['.']
    else:
        search_dirs = target_directories

    for search_dir in search_dirs:
        for root, _, files in os.walk(search_dir):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            lines = f.readlines()
                        for idx, line in enumerate(lines):
                            if query.lower() in line.lower():
                                # Get 2 lines before and after the match for context
                                start = max(0, idx - 2)
                                end = min(len(lines), idx + 3)
                                snippet = ''.join(lines[start:end])
                                results.append(f'File: {file_path}, Line: {idx+1}\n{snippet}\n---')
                    except Exception as e:
                        results.append(f"Error reading {file_path}: {e}")

    if not results:
        return "No relevant code snippets found."
    return '\n'.join(results)

codebase_search_tool = FunctionTool(
    name="codebase_search",
    description="Find snippets of code most relevant to the search query using semantic search.",
    params_json_schema=CodebaseSearchArgs.model_json_schema(),
    on_invoke_tool=run_codebase_search,
)

# Example usage in an agent:
# agent = Agent(
#     name="Code Assistant",
#     instructions="An agent that helps with code-related tasks.",
#     tools=[codebase_search_tool],
#     model=your_model_here,
# )