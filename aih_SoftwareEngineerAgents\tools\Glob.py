from typing import Optional, List
from pydantic import BaseModel, Field
from agents import FunctionTool, RunContextWrapper
from typing import Any
import os
import glob

class GlobArgs(BaseModel):
    pattern: str = Field(
        ...,
        description="The glob pattern to match against (e.e.g., '**/*.py', 'docs/*.md')."
    )
    path: Optional[str] = Field(
        None,
        description='Optional: The absolute path to the directory to search within. If omitted, searches the root directory.'
    )
    case_sensitive: Optional[bool] = Field(
        False,
        description='Optional: Whether the search should be case-sensitive. Defaults to false.'
    )

async def run_glob(
    ctx: RunContextWrapper[Any],
    args: str
) -> str:
    """
    Efficiently finds files matching specific glob patterns.
    """
    parsed = GlobArgs.model_validate_json(args)
    pattern = parsed.pattern
    search_path = parsed.path
    case_sensitive = parsed.case_sensitive

    if search_path and not os.path.isabs(search_path):
        return f"Error: Search path must be absolute: {search_path}"

    try:
        if search_path:
            glob_path = os.path.join(search_path, pattern)
        else:
            glob_path = pattern

        files = glob.glob(glob_path, recursive=True, case_sensitive=case_sensitive)

        if not files:
            return f"No files found matching pattern \"{pattern}"

        return "\n".join(files)
    except Exception as e:
        return f"Error during glob search: {e}"

glob_tool = FunctionTool(
    name="glob",
    description='Efficiently finds files matching specific glob patterns (e.g., `src/**/*.ts`, `**/*.md`), returning absolute paths sorted by modification time (newest first). Ideal for quickly locating files based on their name or path structure, especially in large codebases.',
    params_json_schema=GlobArgs.model_json_schema(),
    on_invoke_tool=run_glob,
)
