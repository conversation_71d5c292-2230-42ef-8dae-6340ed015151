import os
import tempfile
import difflib
import subprocess
import shutil
import asyncio
import json
from pydantic import BaseModel, <PERSON>
from typing import Any
from pathlib import Path


class ModifyToolArgs(BaseModel):
    file_path: str = Field(
        ..., 
        description="Absolute path to the file to modify. Must exist unless creating new file."
    )
    proposed_content: str = Field(
        ..., 
        description="The proposed new content to replace the file with."
    )
    create_if_missing: bool = Field(
        False,
        description="Create file if it doesn't exist. Defaults to False."
    )

async def run_modify_with_editor(
    ctx: Any,
    args: str
) -> str:
    """
    Opens a diff editor to modify file content interactively.
    Waits for editor to close before proceeding.
    Returns unified diff of changes made.
    """
    parsed = ModifyToolArgs.model_validate_json(args)
    file_path = parsed.file_path
    proposed_content = parsed.proposed_content
    create_if_missing = parsed.create_if_missing

    # Validate file path is absolute or not
    if not os.path.isabs(file_path):
        return  "Error: File path must be absolute"

    # Get current content
    current_content = ""
    file_exists = os.path.exists(file_path)
    if file_exists:
        with open(file_path, "r", encoding="utf-8") as f:
            current_content = f.read()
    elif not create_if_missing:
        return f"Error: File not found and create_if_missing=False: {file_path}"

    # Create temp directory and files
    temp_dir = Path(tempfile.mkdtemp(prefix="modify_tool_"))
    old_path = temp_dir / "old_file"
    new_path = temp_dir / "new_file"
    
    try:
        # Write content to temp files
        old_path.write_text(current_content, encoding="utf-8")
        new_path.write_text(proposed_content, encoding="utf-8")

        # Try different editors in order of preference
        editors = [
            ('code', ['--wait', '--diff', str(old_path), str(new_path)]),
            ('vimdiff', [str(old_path), str(new_path)]),
            ('nano', [str(new_path)]),
            ('vi', [str(new_path)]),
            ('notepad++', ['-multiInst', '-nosession', str(new_path)]),
            ('gedit', ['--wait', str(new_path)])
        ]

        editor_found = False
        for editor, args in editors:
            editor_path = shutil.which(editor)
            if editor_path:
                try:
                    # Run editor and wait for it to close
                    subprocess.run([editor_path] + args, check=True)
                    editor_found = True
                    break
                except subprocess.CalledProcessError:
                    continue

        if not editor_found:
            # Fallback: show diff in console
            diff = difflib.unified_diff(
                current_content.splitlines(),
                proposed_content.splitlines(),
                fromfile="Current",
                tofile="Proposed"
            )
            print("No editor found. Showing diff:")
            print("\n".join(diff))
            return "No editor available - showing diff only"

        # Get modified content after editor closed
        modified_content = new_path.read_text(encoding="utf-8")

        # Generate diff
        diff = "\n".join(difflib.unified_diff(
            current_content.splitlines(),
            modified_content.splitlines(),
            fromfile="Current",
            tofile="Modified",
            lineterm=""
        ))

        # Apply changes to original file
        Path(file_path).write_text(modified_content, encoding="utf-8")

        return f"✅ File updated successfully.\n\n--- DIFF ---\n{diff}"

    except Exception as e:
        return f"Error: {str(e)}"
    finally:
        # Clean up temp files
        shutil.rmtree(temp_dir, ignore_errors=True)

# Test function
async def test_modify_tool():
    # Create test file
    test_file = Path(r'C:\Users\<USER>\OneDrive\Desktop\Akeshkumar\forks\nseit_code_agent\requirements copy.txt') 
    original_content = "Original content\nLine 2\nLine 3"
    test_file.write_text(original_content, encoding="utf-8")

    test_args = {
        "file_path": str(test_file),
        "proposed_content": "Modified content\nLine 2\nNew line",
        "create_if_missing": True
    }

    print("=== Testing modify tool ===")
    print(f"Original content:\n{original_content}\n")
    
    result = await run_modify_with_editor(None, json.dumps(test_args))
    
    print("\nResult:")
    print(result)
    print("\nFinal content:")
    print(test_file.read_text(encoding="utf-8"))

    # Clean up
    # test_file.unlink(missing_ok=True)

if __name__ == "__main__":
    asyncio.run(test_modify_tool())